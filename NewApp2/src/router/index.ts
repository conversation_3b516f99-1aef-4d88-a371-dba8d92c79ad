import { createRouter, createWebHistory } from 'vue-router';
import MainLayout from '@/layouts/MainLayout.vue';
import LoginView from '@/views/LoginView.vue';
import CaseManagementView from '@/views/CaseManagementView.vue';
import ImageToTextView from '@/views/ImageToTextView.vue';
import AiGenerateView from '@/views/AiGenerateView.vue';
import MyExamplesView from '@/views/MyExamplesView.vue';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      component: MainLayout,
      meta: { requiresAuth: true },
      redirect: '/cases', // Default to cases view
      children: [
        {
          path: 'cases',
          name: 'cases',
          component: CaseManagementView
    },
    {
          path: 'image-to-text',
          name: 'image-to-text',
          component: ImageToTextView
        },
        {
          path: 'ai-generate',
          name: 'ai-generate',
          component: AiGenerateView
        },
        {
          path: 'my-examples',
          name: 'my-examples',
          component: MyExamplesView
        },
        // Add other child routes for tabs here later
        // e.g. { path: 'image-to-text', component: () => import('@/views/ImageToTextView.vue') }
      ]
    },
    // Fallback route
    { 
      path: '/:pathMatch(.*)*', 
      redirect: '/' 
    }
  ],
});

router.beforeEach((to, from, next) => {
  const requiresAuth = to.meta.requiresAuth;
  const token = localStorage.getItem('token');

  if (requiresAuth && !token) {
    // If route requires auth and user is not logged in, redirect to login
    next('/login');
  } else if (to.path === '/login' && token) {
    // If user is logged in and tries to access login page, redirect to home
    next('/');
  } else {
    // Otherwise, proceed
    next();
  }
});

export default router;
