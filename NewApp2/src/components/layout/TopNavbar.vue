<template>
  <header class="px-4 py-2 border-b border-gray-700/50 bg-gray-900">
    <div class="flex items-center justify-between">
      <!-- Left Section: Logo -->
      <div class="flex items-center">
        <h1 class="text-xl font-bold text-white">像素星云</h1>
      </div>

      <!-- Right Section: Actions & User Menu -->
      <div v-if="auth.isAuthenticated" class="flex items-center space-x-3">
        
        <!-- Credits -->
        <div 
          @click="openBuyCreditsModal" 
          class="flex items-center space-x-2 bg-gray-800/50 px-3 py-1.5 rounded-lg cursor-pointer hover:bg-gray-700/60 transition-colors"
          title="点击购买积分"
        >
          <i class="bi bi-coin text-yellow-400"></i>
          <span class="text-sm text-white font-medium">{{ auth.user?.credits ?? '...' }}</span>
        </div>

        <!-- Notifications Dropdown -->
        <div class="relative" ref="notificationDropdownRef">
          <button @click="toggleNotificationDropdown" class="text-gray-300 hover:text-white p-2 rounded-full hover:bg-gray-700/60 transition-colors" title="通知">
            <i class="bi bi-bell text-lg"></i>
            <span v-if="notificationCount > 0" class="absolute -top-0.5 -right-0.5 flex h-4 w-4">
              <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
              <span class="relative inline-flex rounded-full h-4 w-4 bg-red-500 text-xs text-white items-center justify-center">{{ notificationCount }}</span>
            </span>
          </button>
          <div v-show="isNotificationDropdownOpen" class="absolute right-0 mt-2 w-80 bg-slate-800 border border-slate-700 rounded-md shadow-lg z-20">
            <div class="p-4 border-b border-slate-700">
              <h6 class="font-semibold text-white">通知中心</h6>
            </div>
            <div class="py-1 max-h-80 overflow-y-auto">
              <a href="#" @click.prevent="$emit('open-comments-notifications-modal')" class="flex items-center px-4 py-3 text-sm text-slate-300 hover:bg-slate-700/50">
                <i class="bi bi-chat-left-text-fill me-2 text-slate-400"></i>
                <span>收到的评论</span>
                <span class="ms-auto badge bg-primary rounded-pill" v-if="commentNotificationCount > 0">{{ commentNotificationCount }}</span>
              </a>
              <a href="#" @click.prevent="gotoChangelog" class="flex items-center px-4 py-3 text-sm text-slate-300 hover:bg-slate-700/50">
                <i class="bi bi-journal-text me-2 text-slate-400"></i>
                <span>更新日志</span>
              </a>
            </div>
            <div class="p-2 border-t border-slate-700 text-center">
              <a href="#" class="text-sm text-sky-400 hover:underline">查看所有通知</a>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <button @click="openJoinGroupModal" class="btn-outline-success">
          <i class="bi bi-wechat"></i>
          <span class="hidden sm:inline">加入社群</span>
        </button>
        <button @click="gotoAICanvas" class="btn-primary" :disabled="isCanvasButtonLoading">
          <span v-if="isCanvasButtonLoading" class="ai-canvas-spinner"></span>
          <i v-else class="bi bi-palette-fill"></i>
          <span class="hidden sm:inline">{{ isCanvasButtonLoading ? '跳转中...' : 'AI画布' }}</span>
        </button>
        <button @click="openAddCaseModal" class="btn-primary">
          <i class="bi bi-plus-lg"></i>
          <span class="hidden sm:inline">添加案例</span>
        </button>

        <!-- User Dropdown -->
        <div class="relative" ref="userDropdownRef">
          <button @click="toggleUserDropdown" class="flex items-center space-x-2 p-1 rounded-full hover:bg-gray-700/60 transition-colors">
            <img :src="userAvatar" @error="onAvatarError" alt="用户头像" class="w-8 h-8 rounded-full object-cover" />
            <strong class="hidden md:inline text-sm text-white">{{ auth.user?.nickname || '用户' }}</strong>
            <i class="bi bi-chevron-down text-xs text-gray-400 hidden md:inline"></i>
          </button>
          <div v-show="isUserDropdownOpen" class="absolute right-0 mt-2 w-48 bg-slate-800 border border-slate-700 rounded-md shadow-lg z-20 py-1">
            <a href="#" @click.prevent="$emit('open-profile-modal')" class="dropdown-item">
              <i class="bi bi-person-circle"></i><span>个人资料</span>
            </a>
            <router-link to="/my-examples" @click="closeUserDropdown" class="dropdown-item">
              <i class="bi bi-collection"></i><span>我的案例</span>
            </router-link>
            <a href="#" @click.prevent="$emit('open-liked-cases-modal')" class="dropdown-item">
              <i class="bi bi-heart-fill"></i><span>我点赞的案例</span>
            </a>
            <div class="border-t border-slate-700 my-1"></div>
            <a href="#" @click.prevent="logout" class="dropdown-item">
              <i class="bi bi-box-arrow-right"></i><span>退出</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import defaultAvatar from '@/assets/default-avatar.svg';

const emit = defineEmits([
  'open-add-case-modal', 
  'open-join-group-modal', 
  'open-buy-credits-modal',
  'open-profile-modal',
  'open-my-cases-modal',
  'open-liked-cases-modal',
  'open-comments-notifications-modal'
]);

const auth = useAuthStore();
const router = useRouter();

// Dropdown states
const isUserDropdownOpen = ref(false);
const isNotificationDropdownOpen = ref(false);

// Template refs for dropdowns
const userDropdownRef = ref<HTMLElement | null>(null);
const notificationDropdownRef = ref<HTMLElement | null>(null);

// Example notification count
const notificationCount = ref(3); // This could be a computed prop based on different notification types
const commentNotificationCount = ref(3); // Example static count

// Loading states
const isCanvasButtonLoading = ref(false);

const userAvatar = computed(() => auth.user?.avatar_url || defaultAvatar);

const onAvatarError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = defaultAvatar;
};

// Toggle functions
const toggleUserDropdown = () => {
  isUserDropdownOpen.value = !isUserDropdownOpen.value;
  if (isUserDropdownOpen.value) isNotificationDropdownOpen.value = false;
};

const closeUserDropdown = () => {
  isUserDropdownOpen.value = false;
};

const toggleNotificationDropdown = () => {
  isNotificationDropdownOpen.value = !isNotificationDropdownOpen.value;
  if (isNotificationDropdownOpen.value) isUserDropdownOpen.value = false;
};

// Click outside handler
const handleClickOutside = (event: MouseEvent) => {
  if (userDropdownRef.value && !userDropdownRef.value.contains(event.target as Node)) {
    isUserDropdownOpen.value = false;
  }
  if (notificationDropdownRef.value && !notificationDropdownRef.value.contains(event.target as Node)) {
    isNotificationDropdownOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  auth.fetchCredits();
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// --- Action Handlers ---

const openBuyCreditsModal = () => {
  emit('open-buy-credits-modal');
};

const openJoinGroupModal = () => {
  emit('open-join-group-modal');
};

const gotoAICanvas = async () => {
  isCanvasButtonLoading.value = true;
  try {
    // NOTE: Assumes the token is stored in localStorage by the auth store.
    // Adjust `auth.token` if the token is accessed differently.
    const token = auth.token || localStorage.getItem('token');
    if (!token) {
      throw new Error('未找到认证令牌，请先登录');
    }

    const ssoApiUrl = 'https://caca.yzycolour.top/api/sso/generate-token';
    const response = await fetch(ssoApiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        target_system: 'jaaz'
      })
    });

    if (!response.ok) {
      throw new Error(`生成跳转令牌失败 (HTTP ${response.status})`);
    }

    const data = await response.json();

    if (!data.success || !data.redirect_url) {
      throw new Error(data.message || '生成跳转令牌返回格式无效');
    }

    window.open(data.redirect_url, '_blank');

  } catch (error) {
    console.error('[AI画布] 跳转失败:', error);
    // You might want to use a more user-friendly notification system than alert
    alert(`跳转到AI画布失败: ${error instanceof Error ? error.message : String(error)}`);
  } finally {
    isCanvasButtonLoading.value = false;
  }
};

const openAddCaseModal = () => {
  emit('open-add-case-modal');
};

const logout = () => {
  auth.logout();
  router.push('/login');
};

// Notification dropdown navigation
const gotoChangelog = () => {
  router.push('/changelog');
};

</script>

<style scoped>
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-1.5 px-3 rounded-md flex items-center space-x-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}
.btn-outline-success {
  @apply bg-transparent border border-green-500 text-green-400 hover:bg-green-500/10 hover:text-green-300 font-medium py-1.5 px-3 rounded-md flex items-center space-x-2 transition-colors;
}
.dropdown-item {
  @apply flex items-center px-4 py-2 text-sm text-slate-300 hover:bg-slate-700 hover:text-white;
}
.dropdown-item i {
  @apply mr-3 text-slate-400;
}

/* Spinner for AI Canvas button */
.ai-canvas-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: ai-canvas-spin 1s ease-in-out infinite;
}
@keyframes ai-canvas-spin {
    to { transform: rotate(360deg); }
}
</style>