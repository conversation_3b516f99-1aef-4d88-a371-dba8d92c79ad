<template>
  <div class="px-4 pt-2 border-b border-gray-700">
    <nav class="-mb-px flex flex-wrap gap-x-2">
      <router-link
        v-for="tab in tabs"
        :key="tab.name"
        :to="tab.to"
        v-slot="{ href, navigate, isActive }"
      >
        <a
          :href="href"
          @click="navigate"
          class="block rounded-t-lg py-2 px-3 text-sm font-medium border-x border-t"
          :class="[
            isActive
              ? 'border-gray-700 text-white bg-gray-900'
              : 'border-transparent text-gray-400 hover:border-gray-600 hover:text-gray-300',
          ]"
        >
          <i class="bi mr-2" :class="tab.icon"></i>
          <span>{{ tab.name }}</span>
        </a>
      </router-link>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const tabs = ref([
  { name: '案例管理', icon: 'bi-collection', to: '/cases' },
  { name: '我的案例', icon: 'bi-person-square', to: '/my-examples' },
  { name: '提示词反推 (图生文)', icon: 'bi-image-alt', to: '/image-to-text' },
  { name: 'AI 图片生成', icon: 'bi-stars', to: '/ai-generate' },
  { name: '清晰放大', icon: 'bi-arrows-angle-expand', to: '/upscale' },
  { name: '背景移除', icon: 'bi-eraser-fill', to: '/remove-bg' },
  { name: '位图转 SVG', icon: 'bi-bounding-box-circles', to: '/vectorize' },
  { name: '图片转3D', icon: 'bi-cube', to: '/image-to-3d' },
  { name: '图生视频', icon: 'bi-film', to: '/image-to-video' },
  { name: 'GPT-4o 图片编辑', icon: 'bi-pencil-square', to: '/gpt4o-edit' },
  { name: 'AI 聊天助手', icon: 'bi-chat-dots-fill', to: '/chat' },
  { name: '移除万物', icon: 'bi-scissors', to: '/remove-anything' },
  { name: 'Flux智能编辑图片', icon: 'bi-palette-fill', to: '/flux' },
  { name: '电商专栏', icon: 'bi-shop', to: '/ecommerce' },
  { name: 'AI补光&美颜', icon: 'bi-lightning', to: '/retouch' },
]);

// Admin-only tab can be added conditionally
// { name: '管理中心', icon: 'bi-shield-lock', to: '/admin', adminOnly: true },
</script> 