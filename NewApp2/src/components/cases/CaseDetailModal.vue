<template>
  <div v-if="isOpen" class="modal-backdrop" @click="closeModal">
    <div class="modal-content" @click.stop>
      <!-- Header -->
      <div class="modal-header">
        <h5 class="modal-title">案例详情</h5>
        <button type="button" class="btn-close" @click="closeModal">
          <i class="bi bi-x-lg"></i>
        </button>
      </div>

      <!-- Body -->
      <div class="modal-body">
        <div v-if="isLoading" class="loading-state">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <span>加载案例详情中...</span>
        </div>

        <div v-else-if="caseData" class="container-fluid">
          <div class="row">
            <!-- Left Column: Image -->
            <div class="col-lg-7">
              <div class="detail-images-gallery mb-3">
                <div class="main-image-area text-center">
                  <img 
                    v-if="caseData.cover_image_url && !caseData.cover_image_url.includes('placeholder')"
                    :src="caseData.cover_image_url" 
                    class="img-fluid rounded detail-main-image" 
                    :alt="caseData.title"
                  />
                  <div v-else class="example-detail-placeholder-main-image">
                    <i class="bi bi-image-alt"></i>
                    <p>暂无图片</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column: Details -->
            <div class="col-lg-5">
              <!-- User Info -->
              <div class="d-flex align-items-center justify-content-between mb-3">
                <div class="d-flex align-items-center">
                  <img 
                    :src="caseData.author_avatar_url || defaultAvatar" 
                    :alt="caseData.author_display_name"
                    class="rounded-circle me-2 author-avatar"
                  />
                  <div class="flex-grow-1">
                    <h5 class="mb-0 author-name">{{ caseData.author_display_name }}</h5>
                  </div>
                </div>
                <button class="btn btn-sm btn-outline-secondary" @click="handleShare" title="分享">
                  <i class="bi bi-share-fill me-1"></i> 分享
                </button>
              </div>
              
              <hr class="my-2">

              <!-- Title -->
              <div class="mb-2">
                <h4 class="mb-1 case-title">{{ caseData.title }}</h4>
              </div>

              <!-- Tabs -->
              <div class="detail-tab-container">
                <ul class="nav nav-tabs" role="tablist">
                  <li class="nav-item" role="presentation">
                    <button 
                      :class="['nav-link', { active: activeTab === 'details' }]"
                      @click="activeTab = 'details'"
                      type="button"
                    >
                      详情
                    </button>
                  </li>
                  <li class="nav-item" role="presentation">
                    <button 
                      :class="['nav-link', { active: activeTab === 'comments' }]"
                      @click="activeTab = 'comments'"
                      type="button"
                    >
                      评论 (<span>{{ comments.length }}</span>)
                    </button>
                  </li>
                </ul>
                
                <div class="tab-content">
                  <!-- Details Tab -->
                  <div v-if="activeTab === 'details'" class="tab-pane p-3 rounded-bottom">
                    <h6><i class="bi bi-lightbulb me-1"></i>创意详情</h6>
                    
                    <h6 class="mt-3"><i class="bi bi-blockquote-left me-1"></i>创意描述 (Prompt)</h6>
                    <div class="prompt-text-container mb-2">
                      <pre class="mb-0 prompt-text">{{ caseData.prompt || '无提示词' }}</pre>
                    </div>
                    <button 
                      type="button" 
                      @click="copyPrompt"
                      class="btn btn-sm btn-outline-secondary mb-3"
                    >
                      <i class="bi bi-clipboard me-1"></i> 复制提示词
                    </button>
                    
                    <div class="detail-info-grid">
                      <div class="detail-info-item">
                        <div class="detail-info-label">模型</div>
                        <div class="detail-info-value">{{ caseData.model || 'N/A' }}</div>
                      </div>
                      <div class="detail-info-item">
                        <div class="detail-info-label">分类</div>
                        <div class="detail-info-value">{{ caseData.category_name || 'N/A' }}</div>
                      </div>
                      <div class="detail-info-item">
                        <div class="detail-info-label">创建时间</div>
                        <div class="detail-info-value">{{ formatDate(caseData.created_at) }}</div>
                      </div>
                    </div>

                    <!-- Tags -->
                    <h6 class="mt-3"><i class="bi bi-tags me-1"></i>标签</h6>
                    <div class="tags-container mb-3">
                      <span 
                        v-for="tag in caseData.tags" 
                        :key="tag" 
                        class="badge bg-secondary me-1 mb-1"
                      >
                        {{ tag }}
                      </span>
                      <span v-if="!caseData.tags || caseData.tags.length === 0" class="text-muted">
                        暂无标签
                      </span>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons mt-3">
                      <button 
                        :class="['btn', 'btn-outline-danger', 'me-2', { 'btn-danger': caseData.is_liked_by_current_user }]"
                        @click="toggleLike"
                        :disabled="isLiking"
                      >
                        <i :class="['bi', caseData.is_liked_by_current_user ? 'bi-heart-fill' : 'bi-heart']"></i>
                        {{ caseData.likes }} 点赞
                      </button>
                      <button class="btn btn-outline-secondary" @click="copyPrompt">
                        <i class="bi bi-clipboard"></i> 复制
                      </button>
                    </div>
                  </div>

                  <!-- Comments Tab -->
                  <div v-if="activeTab === 'comments'" class="tab-pane p-3 rounded-bottom">
                    <!-- Comment Form -->
                    <div class="comment-form mb-4">
                      <h6><i class="bi bi-chat-left-text me-1"></i>发表评论</h6>
                      <textarea 
                        v-model="newCommentText"
                        class="form-control mb-2"
                        rows="3"
                        placeholder="发表你的看法..."
                        :disabled="isPostingComment"
                      ></textarea>
                      <button 
                        @click="handlePostComment"
                        :disabled="isPostingComment || !newCommentText.trim()"
                        class="btn btn-primary btn-sm"
                      >
                        <span v-if="isPostingComment">
                          <span class="spinner-border spinner-border-sm me-1"></span>
                          发布中...
                        </span>
                        <span v-else>发表评论</span>
                      </button>
                    </div>

                    <!-- Comments List -->
                    <div class="comments-list">
                      <h6 v-if="comments.length > 0"><i class="bi bi-chat-dots me-1"></i>评论列表</h6>
                      
                      <div v-if="isLoadingComments" class="text-center py-3">
                        <div class="spinner-border spinner-border-sm" role="status">
                          <span class="visually-hidden">加载评论中...</span>
                        </div>
                        <span class="ms-2">加载评论中...</span>
                      </div>

                      <div v-else-if="comments.length === 0" class="text-center py-4 text-muted">
                        <i class="bi bi-chat-square-text fs-1 mb-2 d-block"></i>
                        <p>暂无评论，快来发表第一条评论吧！</p>
                      </div>

                      <div v-else class="comment-item-list">
                        <div 
                          v-for="comment in comments" 
                          :key="comment.id"
                          class="comment-item mb-3 p-3 border rounded"
                        >
                          <div class="d-flex align-items-start">
                            <img 
                              :src="comment.author.avatar_url || defaultAvatar"
                              :alt="comment.author.nickname || comment.author.username"
                              class="rounded-circle me-2 comment-avatar"
                            />
                            <div class="flex-grow-1">
                              <div class="d-flex align-items-center justify-content-between mb-1">
                                <h6 class="mb-0 comment-author">
                                  {{ comment.author.nickname || comment.author.username }}
                                </h6>
                                <small class="text-muted">{{ formatDate(comment.created_at) }}</small>
                              </div>
                              <p class="mb-0 comment-content">{{ comment.content }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="error-state text-center py-4">
          <i class="bi bi-exclamation-triangle fs-1 text-warning mb-2 d-block"></i>
          <p>加载案例详情失败</p>
          <button class="btn btn-outline-primary" @click="loadCaseDetail">重试</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import api from '@/api'

interface CaseData {
  id: number
  title: string
  cover_image_url: string
  author_display_name: string
  author_avatar_url: string
  likes: number
  is_liked_by_current_user: boolean
  model: string
  category_name: string
  prompt: string
  tags: string[]
  created_at: string
}

interface Comment {
  id: number
  content: string
  author: {
    id: number
    username: string
    nickname?: string
    avatar_url?: string
  }
  created_at: string
}

const props = defineProps<{
  isOpen: boolean
  caseId: number | null
}>()

const emit = defineEmits<{
  close: []
}>()

// State
const isLoading = ref(false)
const caseData = ref<CaseData | null>(null)
const activeTab = ref('details')
const comments = ref<Comment[]>([])
const newCommentText = ref('')
const isPostingComment = ref(false)
const isLoadingComments = ref(false)
const isLiking = ref(false)

// Constants
const defaultAvatar = '/images/default-avatar.png'

// Computed
const hasGpt4oEditImages = computed(() => {
  return caseData.value?.source_image_url || caseData.value?.reference_image_url
})

// Methods
const closeModal = () => {
  emit('close')
}

const loadCaseDetail = async () => {
  if (!props.caseId) return
  
  isLoading.value = true
  try {
    const response = await api.get(`/examples/${props.caseId}`)
    caseData.value = {
      id: response.data.id,
      title: response.data.title || '无标题',
      cover_image_url: response.data.image_file ? 
        `https://www.yzycolour.top/prompt-examples/server/uploads/${response.data.image_file}` : 
        '/images/default-avatar.png',
      author_display_name: response.data.author?.nickname || response.data.author?.username || '匿名用户',
      author_avatar_url: response.data.author?.avatar_url ? 
        `https://www.yzycolour.top/prompt-examples/server/uploads/${response.data.author.avatar_url}` : 
        defaultAvatar,
      likes: response.data.likes_count || 0,
      is_liked_by_current_user: response.data.is_liked_by_current_user || false,
      model: response.data.target_model || '未指定',
      category_name: response.data.category || '未分类',
      prompt: response.data.prompt || '无提示词',
      tags: Array.isArray(response.data.tags) ? response.data.tags : [],
      created_at: response.data.created_at
    }
  } catch (error) {
    console.error('Failed to load case detail:', error)
  } finally {
    isLoading.value = false
  }
}

const loadComments = async () => {
  if (!props.caseId) return
  
  isLoadingComments.value = true
  try {
    const response = await api.get(`/examples/${props.caseId}/comments`)
    comments.value = response.data.comments || []
  } catch (error) {
    console.error('Failed to load comments:', error)
    comments.value = []
  } finally {
    isLoadingComments.value = false
  }
}

const handlePostComment = async () => {
  if (!newCommentText.value.trim() || !props.caseId) return
  
  isPostingComment.value = true
  try {
    const response = await api.post(`/examples/${props.caseId}/comments`, {
      content: newCommentText.value.trim()
    })
    
    if (response.data.success) {
      newCommentText.value = ''
      await loadComments() // Reload comments
    }
  } catch (error) {
    console.error('Failed to post comment:', error)
  } finally {
    isPostingComment.value = false
  }
}

const toggleLike = async () => {
  if (!props.caseId || !caseData.value) return
  
  isLiking.value = true
  try {
    const method = caseData.value.is_liked_by_current_user ? 'DELETE' : 'POST'
    const response = await api.request({
      method,
      url: `/examples/${props.caseId}/like`
    })
    
    if (response.data) {
      caseData.value.is_liked_by_current_user = response.data.isLiked
      caseData.value.likes = response.data.likes_count
    }
  } catch (error) {
    console.error('Failed to toggle like:', error)
  } finally {
    isLiking.value = false
  }
}

const copyPrompt = async () => {
  if (!caseData.value?.prompt) return
  
  try {
    await navigator.clipboard.writeText(caseData.value.prompt)
    // You can add a toast notification here
    console.log('Prompt copied to clipboard')
  } catch (error) {
    console.error('Failed to copy prompt:', error)
  }
}

const handleShare = () => {
  // Implement share functionality
  console.log('Share case:', props.caseId)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Watch for prop changes
watch(() => props.caseId, (newId) => {
  if (newId && props.isOpen) {
    loadCaseDetail()
    loadComments()
  }
}, { immediate: true })

watch(() => props.isOpen, (isOpen) => {
  if (isOpen && props.caseId) {
    loadCaseDetail()
    loadComments()
  }
})
</script>

<style scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: rgba(20, 20, 20, 0.95);
  border-radius: 1rem;
  max-width: 90vw;
  max-height: 90vh;
  width: 1200px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: between;
}

.modal-title {
  color: white;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.btn-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  margin-left: auto;
}

.btn-close:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.modal-body {
  padding: 1.5rem;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
  color: white;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: rgba(255, 255, 255, 0.7);
}

.detail-main-image {
  max-height: 500px;
  object-fit: contain;
  border-radius: 0.5rem;
}

.example-detail-placeholder-main-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  color: rgba(255, 255, 255, 0.5);
}

.example-detail-placeholder-main-image i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.author-avatar {
  width: 48px;
  height: 48px;
  object-fit: cover;
}

.author-name {
  font-size: 1rem;
  color: white;
}

.case-title {
  font-size: 1.25rem;
  color: white;
}

.nav-tabs {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-link {
  color: rgba(255, 255, 255, 0.7);
  border: none;
  background: none;
  padding: 0.75rem 1rem;
  border-radius: 0;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.nav-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.05);
}

.nav-link.active {
  color: #6366f1;
  border-bottom-color: #6366f1;
  background: none;
}

.tab-pane {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-top: none;
}

.prompt-text {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  color: rgba(255, 255, 255, 0.9);
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
}

.detail-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.detail-info-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.detail-info-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
}

.detail-info-value {
  font-weight: 500;
  color: white;
}

.tags-container .badge {
  font-size: 0.8rem;
  padding: 0.4rem 0.8rem;
}

.action-buttons .btn {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

.comment-form .form-control {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 0.5rem;
}

.comment-form .form-control:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: #6366f1;
  box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
  color: white;
}

.comment-form .form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.comment-avatar {
  width: 32px;
  height: 32px;
  object-fit: cover;
}

.comment-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.comment-author {
  color: white;
  font-size: 0.9rem;
}

.comment-content {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.error-state {
  color: rgba(255, 255, 255, 0.7);
}

.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-outline-secondary {
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.btn-outline-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.btn-outline-danger {
  border-color: rgba(239, 68, 68, 0.5);
  color: rgba(239, 68, 68, 0.9);
}

.btn-outline-danger:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.7);
  color: #ef4444;
}

.btn-danger {
  background: #ef4444;
  border-color: #ef4444;
  color: white;
}

.btn-primary {
  background: #6366f1;
  border-color: #6366f1;
  color: white;
}

.btn-primary:hover {
  background: #5855eb;
  border-color: #5855eb;
}

.btn-outline-primary {
  border-color: rgba(99, 102, 241, 0.5);
  color: #6366f1;
}

.btn-outline-primary:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: #6366f1;
  color: #6366f1;
}

/* Responsive */
@media (max-width: 768px) {
  .modal-content {
    width: 95vw;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body {
    padding: 1rem;
  }

  .detail-info-grid {
    grid-template-columns: 1fr;
  }
}
</style>
