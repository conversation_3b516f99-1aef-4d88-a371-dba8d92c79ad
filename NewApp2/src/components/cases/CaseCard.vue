<template>
  <div class="case-card group w-full" @click="onCardClick">
    <div class="image-container">
      <img :src="caseData.cover_image_url" :alt="caseData.title" class="case-image" loading="lazy" />
      <!-- The overlay will appear on hover -->
      <div class="overlay">
        <!-- Overlay content -->
        <div>
            <!-- Info, prompt, tags -->
            <div class="info-bar">
              <span class="info-item"><i class="bi bi-aspect-ratio"></i> {{ caseData.model }}</span>
              <span class="info-item"><i class="bi bi-bookmark"></i> {{ caseData.category_name }}</span>
            </div>
            <p class="prompt-preview">{{ caseData.prompt }}</p>
            <div class="tags-container">
              <span v-for="tag in caseData.tags.slice(0, 3)" :key="tag" class="tag">{{ tag }}</span>
              <span v-if="caseData.tags.length > 3" class="tag">...</span>
            </div>
        </div>
        <div class="card-footer-in-overlay">
           <!-- Author, actions -->
          <div class="author-info">
            <img :src="caseData.author_avatar_url" alt="Author" class="author-avatar" @error="onAvatarError">
            <span>{{ caseData.author_display_name }}</span>
          </div>
          <div class="card-actions">
            <button @click.stop="toggleLike" class="action-btn like-btn" :class="{ 'liked': isLiked }" :title="isLiked ? '取消点赞' : '点赞'">
              <i class="bi" :class="isLiked ? 'bi-heart-fill' : 'bi-heart'"></i>
              <span class="ml-1">{{ likeCount }}</span>
            </button>
            <button @click.stop="copyPrompt" class="action-btn" title="复制提示词">
              <i class="bi bi-copy"></i>
            </button>
            <button @click.stop="share" class="action-btn" title="分享">
              <i class="bi bi-share"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { PropType } from 'vue';
import api from '@/api';
import defaultAvatar from '@/assets/default-avatar.svg';

interface Case {
  id: number;
  title: string;
  cover_image_url: string;
  author_display_name: string;
  author_avatar_url: string;
  likes: number;
  is_liked_by_current_user: boolean;
  model: string;
  category_name: string;
  prompt: string;
  tags: string[];
}

const props = defineProps({
  caseData: {
    type: Object as PropType<Case>,
    required: true,
  },
});

const emit = defineEmits(['open-detail']);

const isLiked = ref(props.caseData.is_liked_by_current_user);
const likeCount = ref(props.caseData.likes);

const onAvatarError = (event: Event) => {
  (event.target as HTMLImageElement).src = defaultAvatar;
};

const toggleLike = async () => {
  const wasLiked = isLiked.value;
  const originalCount = likeCount.value;

  try {
    const method = wasLiked ? 'DELETE' : 'POST';
    const response = await api.request({
      method,
      url: `/examples/${props.caseData.id}/like`
    });

    // Update with server response - 匹配原始admin代码的响应格式
    if (response.data) {
      isLiked.value = response.data.isLiked;
      likeCount.value = response.data.likes_count;
    }
  } catch (error: any) {
    console.error(`Failed to toggle like for case ${props.caseData.id}:`, error);

    // 处理认证错误
    if (error.response?.status === 401 || error.response?.status === 403) {
      // 可以触发重新登录或显示错误消息
      console.error('Authentication error, please login again');
      return;
    }

    // 显示错误消息
    const errorMsg = error.response?.data?.error || '操作失败，请重试';
    console.error('Like operation failed:', errorMsg);
  }
};

const copyPrompt = () => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(props.caseData.prompt).then(() => {
      console.log('Prompt copied!');
    }).catch(err => {
      console.error('Failed to copy prompt: ', err);
    });
  }
};

const share = () => {
  console.log('Sharing case:', props.caseData.id);
  alert('分享功能待实现');
};

const onCardClick = () => {
  emit('open-detail', props.caseData.id);
};
</script>

<style scoped>
.case-card {
  @apply bg-slate-800 rounded-lg overflow-hidden shadow-lg transition-all duration-300 ease-in-out transform hover:-translate-y-1 hover:shadow-cyan-500/30 cursor-pointer flex flex-col break-inside-avoid;
  border: 1px solid transparent;
}
.case-card:hover {
  border-color: #22d3ee; /* cyan-400 */
}

.image-container {
  @apply relative w-full;
  /* No aspect ratio here */
}

.case-image {
  @apply w-full h-auto block bg-slate-900; /* h-auto is key */
}

.overlay {
  @apply absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4 flex flex-col justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300;
}

.info-bar {
  @apply flex items-center justify-between text-xs text-slate-300 mb-2;
}
.info-item {
  @apply bg-black/50 backdrop-blur-sm rounded-full px-2 py-1 flex items-center gap-1;
}

.prompt-preview {
  @apply text-sm text-white font-semibold mb-2 overflow-hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* Make space for footer */
  -webkit-box-orient: vertical;  
  text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}

.tags-container {
  @apply mt-auto flex flex-wrap gap-2;
}

.tag {
  @apply bg-gray-700 text-gray-300 text-xs font-medium px-2 py-1 rounded;
}

.card-footer-in-overlay {
  @apply flex items-center justify-between pt-3 border-t border-gray-500/40;
}

.author-info {
  @apply flex items-center text-xs text-gray-300;
}

.author-avatar {
  @apply w-6 h-6 rounded-full mr-2 object-cover;
}

.card-actions {
  @apply flex items-center space-x-3;
}

.action-btn {
  @apply text-gray-400 hover:text-white transition-colors duration-200;
}

.action-btn .bi {
  @apply text-sm;
}

.like-btn.liked {
  @apply text-pink-500;
}
</style> 