<template>
  <div class="bg-gray-800/50 p-4 rounded-lg mb-4 border border-gray-700/50">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <!-- Search Input -->
      <div class="md:col-span-1">
        <label for="searchInput" class="block text-sm font-medium text-gray-300 mb-1">搜索</label>
        <div class="relative">
          <input 
            type="text" 
            id="searchInput"
            v-model="searchQuery"
            placeholder="搜索标题、提示词..."
            class="form-input w-full pl-10"
          />
          <span class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
            <i class="bi bi-search"></i>
          </span>
        </div>
      </div>
      
      <!-- Category Filter -->
      <div>
        <label for="categoryFilter" class="block text-sm font-medium text-gray-300 mb-1">分类筛选</label>
        <CustomDropdown v-model="selectedCategory" :options="categories" />
      </div>

      <!-- Model Filter -->
      <div>
        <label for="modelFilter" class="block text-sm font-medium text-gray-300 mb-1">模型筛选</label>
        <CustomDropdown v-model="selectedModel" :options="models" />
      </div>

      <!-- Sort Filter -->
      <div>
        <label for="sortFilter" class="block text-sm font-medium text-gray-300 mb-1">排序方式</label>
        <CustomDropdown v-model="selectedSort" :options="sorts" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import CustomDropdown from '@/components/common/CustomDropdown.vue';
import apiClient from '@/api';

const emit = defineEmits(['filter-change', 'search']);

interface Option {
  value: string | number;
  text: string;
}

// Search query with debouncing
const searchQuery = ref('');
let debounceTimer: number;

watch(searchQuery, (newValue) => {
  clearTimeout(debounceTimer);
  debounceTimer = window.setTimeout(() => {
    emitFilterChange();
  }, 300); // 300ms debounce
});


// Categories will be fetched from API
const categories = ref<Option[]>([]);

async function fetchCategories() {
  try {
    const { data } = await apiClient.get('/categories');
    // Handle different possible response formats
    const categoriesData = data.categories || data;
    const formattedCategories = categoriesData.map((cat: any) => ({
      value: cat.slug || cat.id,
      text: cat.name,
    }));
    // Add the "All Categories" option at the beginning
    categories.value = [{ value: '', text: '全部分类' }, ...formattedCategories];
    selectedCategory.value = categories.value[0];
  } catch (error) {
    console.error('Failed to fetch categories:', error);
    // Fallback to default categories
    categories.value = [
      { value: '', text: '全部分类' },
      { value: 'people', text: '人物' },
      { value: 'landscape', text: '风景' },
      { value: 'animals', text: '动物' },
      { value: 'architecture', text: '建筑' },
      { value: 'art', text: '艺术' },
    ];
    selectedCategory.value = categories.value[0];
  }
}

onMounted(() => {
  fetchCategories();
});


// Static options for models and sorts
const models = ref<Option[]>([
  { value: '', text: '全部模型' },
  { value: 'MJ', text: 'MJ' },
  { value: 'FLUX', text: 'FLUX' },
  { value: 'FLUX-多图', text: 'FLUX-多图' },
  { value: 'SDXL', text: 'SDXL' },
  { value: '超级生图', text: '超级生图' },
  { value: '高级生图', text: '高级生图' },
  { value: 'GPT4O', text: 'GPT4O' },
  { value: 'GPT4O-编辑', text: 'GPT4O-编辑' },
]);
const sorts = ref<Option[]>([
  { value: 'date_desc', text: '最新发布' },
  { value: 'likes_desc', text: '最多点赞' },
]);

const selectedCategory = ref<Option>({ value: '', text: '加载中...' }); // Initial state
const selectedModel = ref(models.value[0]);
const selectedSort = ref(sorts.value[0]);

const emitFilterChange = () => {
  const filters: any = {
    sort: selectedSort.value.value,
  };

  // 只有当值不为空时才添加到筛选条件中
  if (searchQuery.value.trim()) {
    filters.search = searchQuery.value.trim();
  }

  if (selectedCategory.value.value) {
    filters.category_slug = selectedCategory.value.value;
  }

  if (selectedModel.value.value) {
    filters.target_model = selectedModel.value.value;
  }

  emit('filter-change', filters);
};

watch([selectedCategory, selectedModel, selectedSort], () => {
  emitFilterChange();
}, { deep: true, immediate: true });
</script>

<style scoped>
.form-input {
  @apply appearance-none bg-gray-900/50 border border-gray-700/50 text-gray-200 rounded-lg px-4 py-2 focus:bg-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/50 outline-none transition-all duration-300 w-full;
}
.form-select {
  @apply appearance-none bg-gray-900/50 border border-gray-700/50 text-gray-200 rounded-lg px-4 py-2 focus:bg-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/50 outline-none transition-all duration-300;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}
</style> 