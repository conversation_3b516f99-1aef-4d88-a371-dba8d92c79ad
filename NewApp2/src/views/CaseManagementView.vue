<template>
  <div class="case-management-container">
    <CaseFilters @filter-change="handleFilterChange" />

    <div v-if="isInitialLoading" class="loading-indicator">
      <p>正在加载案例...</p>
    </div>

    <div v-else-if="cases.length > 0">
      <div class="masonry-container">
        <div v-for="item in cases" :key="item.id" class="masonry-item">
          <CaseCard :case-data="item" @open-detail="openCaseDetailModal" />
        </div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      <p>没有找到符合条件的案例。</p>
    </div>

    <CaseDetailModal :is-open="isModalOpen" :case-id="selectedCaseId" @close="isModalOpen = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import CaseFilters from '@/components/cases/CaseFilters.vue';
import CaseCard from '@/components/cases/CaseCard.vue';
import CaseDetailModal from '@/components/cases/CaseDetailModal.vue';
import api from '@/api';
import defaultAvatar from '@/assets/default-avatar.svg';

const props = defineProps({
  scrollContainer: {
    type: Object as () => HTMLElement | null,
    default: null,
  },
});

const emit = defineEmits(['update:loading', 'update:hasMore']);

const cases = ref<any[]>([]);
const currentPage = ref(1);
const isInitialLoading = ref(true);
const currentFilters = ref({});

const fetchCases = async (page = 1) => {
  emit('update:loading', true);
  if (page === 1) {
    isInitialLoading.value = true;
  }

  try {
    // 构建API URL和参数，匹配原始admin代码的逻辑
    const params = new URLSearchParams({
      page: page.toString(),
      limit: '20',
      sort: currentFilters.value.sort || 'date_desc'
    });

    // 添加模型筛选
    if (currentFilters.value.target_model) {
      params.append('target_model', currentFilters.value.target_model === '_NULL_' ? '' : currentFilters.value.target_model);
    }

    // 添加分类筛选
    if (currentFilters.value.category_slug) {
      params.append('category_slug', currentFilters.value.category_slug);
    }

    // 确定API URL
    let apiUrl = '/examples';
    if (currentFilters.value.search) {
      apiUrl = '/examples/search';
      params.append('query', currentFilters.value.search);
    }

    const response = await api.get(`${apiUrl}?${params.toString()}`);

    const newCases = response.data.examples.map((c: any) => ({
        id: c.id,
        title: c.title || '无标题',
        cover_image_url: c.image_file ? `https://www.yzycolour.top/prompt-examples/server/uploads/${c.image_file}` : defaultAvatar,
        author_display_name: c.author?.nickname || c.author?.username || '匿名用户',
        author_avatar_url: c.author?.avatar_url ? `https://www.yzycolour.top/prompt-examples/server/uploads/${c.author.avatar_url}` : defaultAvatar,
        likes: c.likes_count || 0,
        is_liked_by_current_user: c.is_liked_by_current_user || false,
        model: c.target_model || '未指定',
        category_name: c.category || '未分类',
        prompt: c.prompt || '无提示词',
        tags: Array.isArray(c.tags) ? c.tags : []
    }));

    if (page === 1) {
      cases.value = newCases;
    } else {
      cases.value = [...cases.value, ...newCases];
    }

    const hasMore = response.data.pagination ? response.data.pagination.currentPage < response.data.pagination.totalPages : false;
    emit('update:hasMore', hasMore);

    currentPage.value = page;

  } catch (error) {
    console.error('Failed to fetch cases:', error);
    emit('update:hasMore', false);
  } finally {
    emit('update:loading', false);
    if (page === 1) {
      isInitialLoading.value = false;
    }
  }
};

defineExpose({
    loadMore: () => fetchCases(currentPage.value + 1)
});

const handleFilterChange = (filters: any) => {
  if (props.scrollContainer) {
      props.scrollContainer.scrollTo(0, 0);
  }
  currentFilters.value = filters;
  fetchCases(1);
};

onMounted(() => {
  fetchCases(1);
});

const isModalOpen = ref(false);
const selectedCaseId = ref<number | null>(null);

const openCaseDetailModal = (caseId: number) => {
  selectedCaseId.value = caseId;
  isModalOpen.value = true;
};
</script>

<style scoped>
.case-management-container {
  padding: 1rem;
}

.masonry-container {
  @apply gap-4;
  column-count: 1;
}
@media (min-width: 640px) {
  .masonry-container { column-count: 2; }
}
@media (min-width: 1024px) {
  .masonry-container { column-count: 3; }
}
@media (min-width: 1280px) {
  .masonry-container { column-count: 4; }
}

.masonry-item {
  @apply mb-4 break-inside-avoid;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #9ca3af; /* text-gray-400 */
}

.empty-state {
  text-align: center;
  padding-top: 5rem;
  color: #6b7280; /* text-gray-500 */
}

.load-more-trigger {
  height: 50px;
}
.bottom-loader {
  padding: 2rem 0;
}
</style> 