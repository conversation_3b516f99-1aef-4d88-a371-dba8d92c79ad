<template>
  <div class="min-h-screen bg-[#0a0a0a] flex items-center justify-center p-5 relative overflow-hidden">
    <!-- Spotlight Effects -->
    <div class="spotlight top-left"></div>
    <div class="spotlight top-right"></div>
    
    <!-- Grid Background -->
    <div class="grid-background"></div>

    <div class="login-container">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold bg-gradient-to-b from-gray-200 to-white bg-clip-text text-transparent">
          像素星云AI助手
        </h1>
        <p class="text-gray-400 mt-2">{{ formTitle }}</p>
      </div>
      
      <div v-if="errorMessage" class="bg-red-900/50 border border-red-500/30 text-red-300 text-sm rounded-lg p-3 mb-4 text-center">
        {{ errorMessage }}
      </div>
      <div v-if="successMessage" class="bg-green-900/50 border border-green-500/30 text-green-300 text-sm rounded-lg p-3 mb-4 text-center">
        {{ successMessage }}
      </div>

      <!-- 测试账号提示 -->
      <div v-if="formMode === 'login'" class="bg-blue-900/30 border border-blue-500/30 text-blue-300 text-sm rounded-lg p-3 mb-4">
        <div class="text-center mb-2">
          <i class="bi bi-info-circle me-1"></i>
          测试账号
        </div>
        <div class="text-xs space-y-1">
          <div>用户名: <span class="font-mono">test</span></div>
          <div>密码: <span class="font-mono">123456</span></div>
        </div>
        <button
          type="button"
          @click="fillTestAccount"
          class="w-full mt-2 text-xs bg-blue-600/20 hover:bg-blue-600/30 border border-blue-500/30 rounded px-2 py-1 transition-colors"
        >
          一键填入测试账号
        </button>
      </div>

      <!-- Login & Register Form -->
      <form v-if="formMode !== 'forgot'" @submit.prevent="handleSubmit">
        <div class="mb-4">
          <input 
            v-model="identifier"
            :type="formMode === 'register' ? 'email' : 'text'"
            :placeholder="formMode === 'register' ? '请输入注册邮箱' : '邮箱或用户名'"
            required
            class="form-control"
          />
        </div>
        
        <div class="relative mb-4">
          <input 
            v-model="password"
            :type="passwordFieldType" 
            placeholder="密码" 
            required
            class="form-control"
          />
          <button type="button" @click="togglePasswordVisibility" class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-400 hover:text-white">
            <i class="bi" :class="passwordFieldType === 'password' ? 'bi-eye-slash' : 'bi-eye'"></i>
          </button>
        </div>
        
        <div v-if="formMode === 'register'" class="relative mb-4">
          <input 
            v-model="confirmPassword"
            type="password" 
            placeholder="确认密码" 
            required
            class="form-control"
          />
        </div>
        
        <div class="flex items-center justify-between mb-6">
          <label class="flex items-center text-sm text-gray-400 select-none">
            <input type="checkbox" v-model="rememberMe" class="form-checkbox">
            <span class="ml-2">记住我</span>
          </label>
          <a @click.prevent="switchToForgot" href="#" class="text-sm text-indigo-400 hover:text-indigo-300 hover:underline">
            忘记密码?
          </a>
        </div>
        
        <button type="submit" class="w-full btn-primary" :disabled="isLoading">
          <span v-if="isLoading" class="animate-spin mr-2"><i class="bi bi-arrow-repeat"></i></span>
          {{ isLoading ? '处理中...' : (formMode === 'login' ? '登 录' : '注 册') }}
        </button>
        
        <div class="text-center mt-4 text-sm">
          <span class="text-gray-400">{{ formMode === 'login' ? '还没有账号？' : '已有账号？' }}</span>
          <a @click.prevent="toggleFormMode" href="#" class="ml-1 text-indigo-400 hover:text-indigo-300 hover:underline font-medium">
            {{ formMode === 'login' ? '立即注册' : '立即登录' }}
          </a>
        </div>
      </form>

      <!-- Forgot Password Form -->
      <form v-else @submit.prevent="handleForgotPassword">
        <div class="mb-4">
          <input 
            v-model="identifier"
            type="email" 
            placeholder="请输入注册邮箱" 
            required
            class="form-control"
          />
        </div>
        <button type="submit" class="w-full btn-primary" :disabled="isLoading">
           <span v-if="isLoading" class="animate-spin mr-2"><i class="bi bi-arrow-repeat"></i></span>
          {{ isLoading ? '发送中...' : '发送重置链接' }}
        </button>
        <div class="text-center mt-4 text-sm">
          <a @click.prevent="switchToLogin" href="#" class="text-indigo-400 hover:text-indigo-300 hover:underline">
            返回登录
          </a>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

type FormMode = 'login' | 'register' | 'forgot';

const formMode = ref<FormMode>('login');
const identifier = ref('');
const password = ref('');
const confirmPassword = ref('');
const rememberMe = ref(false);

const passwordFieldType = ref<'password' | 'text'>('password');

const isLoading = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

const router = useRouter();
const auth = useAuthStore();

const formTitle = computed(() => {
  if (formMode.value === 'login') return '登录您的账号';
  if (formMode.value === 'register') return '创建新账号';
  return '找回您的密码';
});

const togglePasswordVisibility = () => {
  passwordFieldType.value = passwordFieldType.value === 'password' ? 'text' : 'password';
};

const clearMessages = () => {
  errorMessage.value = '';
  successMessage.value = '';
};

const toggleFormMode = () => {
  formMode.value = formMode.value === 'login' ? 'register' : 'login';
  clearMessages();
};

const switchToForgot = () => {
  formMode.value = 'forgot';
  clearMessages();
};

const switchToLogin = () => {
  formMode.value = 'login';
  clearMessages();
};

const fillTestAccount = () => {
  identifier.value = 'test';
  password.value = '123456';
  clearMessages();
};

const handleSubmit = () => {
  if (formMode.value === 'login') {
    handleLogin();
  } else {
    handleRegister();
  }
};

const handleLogin = async () => {
  isLoading.value = true;
  clearMessages();
  try {
    await auth.login({ identifier: identifier.value, password: password.value });
    // Success navigation is handled inside the auth store
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || '登录失败，请检查您的凭据。';
  } finally {
    isLoading.value = false;
  }
};

const handleRegister = async () => {
  if (password.value !== confirmPassword.value) {
    errorMessage.value = '两次输入的密码不一致。';
    return;
  }
  isLoading.value = true;
  clearMessages();
  try {
    // Assuming auth store has a register method
    // await auth.register({ email: identifier.value, password: password.value });
    successMessage.value = '注册请求已发送，请检查您的邮箱以完成验证。';
    setTimeout(() => {
      switchToLogin();
    }, 3000);
  } catch (error: any) {
    errorMessage.value = error.response?.data?.message || '注册失败，请重试。';
  } finally {
    isLoading.value = false;
  }
};

const handleForgotPassword = async () => {
  isLoading.value = true;
  clearMessages();
  try {
    // Assuming auth store has a forgotPassword method
    // await auth.forgotPassword({ email: identifier.value });
    successMessage.value = '如果邮箱存在，密码重置链接已发送。';
  } catch (error: any) {
     successMessage.value = '如果邮箱存在，密码重置链接已发送。';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style>
/* Global styles for the page background and effects */
.grid-background {
  content: '';
  position: fixed;
  inset: 0;
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 0;
  opacity: 0.2;
  pointer-events: none;
}

.spotlight {
  position: fixed;
  top: 0;
  width: 50vmax;
  height: 50vmax;
  background: radial-gradient(circle at center, rgba(106, 13, 173, 0.15), transparent 60%);
  z-index: 0;
  pointer-events: none;
  opacity: 0.6;
  animation: pulseLight 10s infinite alternate ease-in-out;
}

.spotlight.top-left {
  left: 0;
  transform: translate(-40%, -40%);
  animation-delay: -5s;
}

.spotlight.top-right {
  right: 0;
  transform: translate(40%, -40%);
}

@keyframes pulseLight {
  from { opacity: 0.4; transform: scale(0.95); }
  to { opacity: 0.7; transform: scale(1.05); }
}
</style>

<style scoped>
.login-container {
  width: 100%;
  max-width: 420px;
  padding: 2.5rem;
  background: rgba(20, 20, 20, 0.75);
  border-radius: 1rem;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0));
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.form-control {
  @apply w-full bg-white/5 border border-white/10 rounded-lg px-4 py-3 text-gray-200 placeholder-gray-400;
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500;
  transition: all 0.2s ease-in-out;
}

.btn-primary {
  @apply w-full text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 ease-in-out;
  @apply bg-gradient-to-r from-gray-700 via-gray-800 to-gray-900;
  @apply hover:shadow-lg hover:shadow-indigo-500/20 hover:-translate-y-px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}
.btn-primary:disabled {
  @apply bg-gray-600 cursor-not-allowed hover:shadow-none hover:-translate-y-0;
}

.form-checkbox {
  @apply w-4 h-4 text-indigo-500 bg-gray-800 border-gray-600 rounded focus:ring-indigo-500 focus:ring-offset-gray-900;
}
</style>