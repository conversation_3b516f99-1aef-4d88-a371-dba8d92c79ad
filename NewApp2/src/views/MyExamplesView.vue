<template>
  <div class="my-examples-container">
    <!-- Header -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="bi bi-collection me-2"></i>
        我的案例
      </h1>
      <p class="page-subtitle">管理您创建的所有AI生成案例</p>
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <div class="row align-items-center">
        <div class="col-md-6">
          <div class="input-group">
            <input 
              v-model="searchQuery"
              type="text" 
              class="form-control" 
              placeholder="搜索我的案例..."
              @input="handleSearch"
            />
            <span class="input-group-text">
              <i class="bi bi-search"></i>
            </span>
          </div>
        </div>
        <div class="col-md-3">
          <select v-model="selectedModel" @change="handleFilterChange" class="form-select">
            <option value="">全部模型</option>
            <option value="MJ">MJ</option>
            <option value="FLUX">FLUX</option>
            <option value="FLUX-多图">FLUX-多图</option>
            <option value="SDXL">SDXL</option>
            <option value="超级生图">超级生图</option>
            <option value="高级生图">高级生图</option>
            <option value="GPT4O">GPT4O</option>
            <option value="GPT4O-编辑">GPT4O-编辑</option>
          </select>
        </div>
        <div class="col-md-3">
          <select v-model="sortBy" @change="handleFilterChange" class="form-select">
            <option value="date_desc">最新创建</option>
            <option value="date_asc">最早创建</option>
            <option value="likes_desc">最多点赞</option>
            <option value="title_asc">标题A-Z</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="content-section">
      <!-- Loading State -->
      <div v-if="isLoading && examples.length === 0" class="loading-state">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2">加载我的案例中...</p>
      </div>

      <!-- Empty State -->
      <div v-else-if="examples.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="bi bi-journal-x"></i>
        </div>
        <h3>还没有创建任何案例</h3>
        <p>开始创建您的第一个AI生成案例吧！</p>
        <router-link to="/ai-generate" class="btn btn-primary">
          <i class="bi bi-plus-lg me-1"></i>
          创建案例
        </router-link>
      </div>

      <!-- Examples Grid -->
      <div v-else>
        <div class="examples-grid">
          <div 
            v-for="example in examples" 
            :key="example.id"
            class="example-card"
            @click="openDetailModal(example.id)"
          >
            <div class="card-image">
              <img 
                :src="example.cover_image_url" 
                :alt="example.title"
                class="img-fluid"
                @error="handleImageError"
              />
              <div class="card-overlay">
                <div class="overlay-actions">
                  <button 
                    @click.stop="toggleLike(example)"
                    :class="['action-btn', 'like-btn', { 'liked': example.is_liked_by_current_user }]"
                  >
                    <i :class="['bi', example.is_liked_by_current_user ? 'bi-heart-fill' : 'bi-heart']"></i>
                    <span>{{ example.likes_count }}</span>
                  </button>
                  <button @click.stop="copyPrompt(example.prompt)" class="action-btn">
                    <i class="bi bi-clipboard"></i>
                  </button>
                  <button @click.stop="editExample(example)" class="action-btn">
                    <i class="bi bi-pencil"></i>
                  </button>
                  <button @click.stop="deleteExample(example.id)" class="action-btn delete-btn">
                    <i class="bi bi-trash"></i>
                  </button>
                </div>
              </div>
            </div>
            <div class="card-content">
              <h5 class="card-title">{{ example.title }}</h5>
              <p class="card-prompt">{{ truncateText(example.prompt, 100) }}</p>
              <div class="card-meta">
                <span class="model-badge">{{ example.target_model || '未指定' }}</span>
                <span class="category-badge">{{ example.category || '未分类' }}</span>
              </div>
              <div class="card-footer">
                <small class="text-muted">{{ formatDate(example.created_at) }}</small>
                <div class="card-stats">
                  <span class="likes">
                    <i class="bi bi-heart"></i>
                    {{ example.likes_count }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More -->
        <div v-if="hasMore" class="load-more-section">
          <button 
            @click="loadMore"
            :disabled="isLoading"
            class="btn btn-outline-primary"
          >
            <span v-if="isLoading">
              <span class="spinner-border spinner-border-sm me-1"></span>
              加载中...
            </span>
            <span v-else>加载更多</span>
          </button>
        </div>

        <!-- Pagination Info -->
        <div class="pagination-info text-center mt-3">
          <small class="text-muted">
            已显示 {{ examples.length }} / {{ totalCount }} 个案例
          </small>
        </div>
      </div>
    </div>

    <!-- Detail Modal -->
    <CaseDetailModal 
      :is-open="isDetailModalOpen" 
      :case-id="selectedCaseId" 
      @close="closeDetailModal" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import api from '@/api'
import CaseDetailModal from '@/components/cases/CaseDetailModal.vue'

interface MyExample {
  id: number
  title: string
  prompt: string
  category: string
  target_model?: string
  image_file?: string
  cover_image_url: string
  tags: string[]
  likes_count: number
  is_liked_by_current_user: boolean
  created_at: string
  updated_at?: string
}

// State
const router = useRouter()
const examples = ref<MyExample[]>([])
const isLoading = ref(false)
const searchQuery = ref('')
const selectedModel = ref('')
const sortBy = ref('date_desc')
const currentPage = ref(1)
const totalPages = ref(1)
const totalCount = ref(0)
const hasMore = ref(true)

// Modal state
const isDetailModalOpen = ref(false)
const selectedCaseId = ref<number | null>(null)

// Computed
const filteredExamples = computed(() => {
  return examples.value
})

// Methods
const loadMyExamples = async (page = 1, reset = false) => {
  if (isLoading.value) return

  isLoading.value = true
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: '12',
      sort: sortBy.value
    })

    if (selectedModel.value) {
      params.append('target_model', selectedModel.value)
    }

    if (searchQuery.value.trim()) {
      params.append('search', searchQuery.value.trim())
    }

    const response = await api.get(`/examples/users/me/examples?${params.toString()}`)
    
    const newExamples = response.data.examples.map((example: any) => ({
      ...example,
      cover_image_url: example.image_file ? 
        `https://www.yzycolour.top/prompt-examples/server/uploads/${example.image_file}` : 
        '/images/default-avatar.png'
    }))

    if (reset || page === 1) {
      examples.value = newExamples
    } else {
      examples.value.push(...newExamples)
    }

    currentPage.value = response.data.pagination.currentPage
    totalPages.value = response.data.pagination.totalPages
    totalCount.value = response.data.pagination.totalCount
    hasMore.value = currentPage.value < totalPages.value

  } catch (error) {
    console.error('Failed to load my examples:', error)
  } finally {
    isLoading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadMyExamples(1, true)
}

const handleFilterChange = () => {
  currentPage.value = 1
  loadMyExamples(1, true)
}

const loadMore = () => {
  if (hasMore.value && !isLoading.value) {
    loadMyExamples(currentPage.value + 1, false)
  }
}

const toggleLike = async (example: MyExample) => {
  try {
    const method = example.is_liked_by_current_user ? 'DELETE' : 'POST'
    const response = await api.request({
      method,
      url: `/examples/${example.id}/like`
    })

    if (response.data) {
      example.is_liked_by_current_user = response.data.isLiked
      example.likes_count = response.data.likes_count
    }
  } catch (error) {
    console.error('Failed to toggle like:', error)
  }
}

const copyPrompt = async (prompt: string) => {
  try {
    await navigator.clipboard.writeText(prompt)
    // Add toast notification here
    console.log('Prompt copied to clipboard')
  } catch (error) {
    console.error('Failed to copy prompt:', error)
  }
}

const editExample = (example: MyExample) => {
  // Navigate to edit page or open edit modal
  console.log('Edit example:', example.id)
}

const deleteExample = async (exampleId: number) => {
  if (!confirm('确定要删除这个案例吗？此操作不可撤销。')) {
    return
  }

  try {
    await api.delete(`/examples/${exampleId}`)
    examples.value = examples.value.filter(e => e.id !== exampleId)
    totalCount.value = Math.max(0, totalCount.value - 1)
    console.log('Example deleted successfully')
  } catch (error) {
    console.error('Failed to delete example:', error)
  }
}

const openDetailModal = (caseId: number) => {
  selectedCaseId.value = caseId
  isDetailModalOpen.value = true
}

const closeDetailModal = () => {
  isDetailModalOpen.value = false
  selectedCaseId.value = null
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/default-avatar.png'
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Lifecycle
onMounted(() => {
  loadMyExamples(1, true)
})
</script>

<style scoped>
.my-examples-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
  color: white;
  padding: 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.page-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.filters-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-control, .form-select {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 0.5rem;
}

.form-control:focus, .form-select:focus {
  background: rgba(255, 255, 255, 0.12);
  border-color: #6366f1;
  box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
  color: white;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.input-group-text {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
}

.loading-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.3);
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: white;
  margin-bottom: 0.5rem;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1.5rem;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.example-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.example-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(99, 102, 241, 0.5);
}

.card-image {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.example-card:hover .card-image img {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.example-card:hover .card-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.2s ease;
  cursor: pointer;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.like-btn.liked {
  background: rgba(239, 68, 68, 0.8);
}

.delete-btn:hover {
  background: rgba(239, 68, 68, 0.8);
}

.card-content {
  padding: 1rem;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.card-prompt {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

.card-meta {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.model-badge, .category-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.model-badge {
  background: rgba(99, 102, 241, 0.2);
  color: #a5b4fc;
  border: 1px solid rgba(99, 102, 241, 0.3);
}

.category-badge {
  background: rgba(34, 197, 94, 0.2);
  color: #86efac;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.card-footer {
  display: flex;
  justify-content: between;
  align-items: center;
  font-size: 0.8rem;
}

.card-stats {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: rgba(255, 255, 255, 0.6);
}

.load-more-section {
  text-align: center;
  margin: 2rem 0;
}

.pagination-info {
  color: rgba(255, 255, 255, 0.6);
}

.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5855eb, #7c3aed);
  transform: translateY(-1px);
}

.btn-outline-primary {
  border-color: rgba(99, 102, 241, 0.5);
  color: #6366f1;
  background: transparent;
}

.btn-outline-primary:hover {
  background: rgba(99, 102, 241, 0.1);
  border-color: #6366f1;
  color: #6366f1;
}

/* Responsive */
@media (max-width: 768px) {
  .my-examples-container {
    padding: 1rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .examples-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }

  .filters-section {
    padding: 1rem;
  }
}
</style>
