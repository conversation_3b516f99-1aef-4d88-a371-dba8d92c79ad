{"version": 3, "names": ["_arrayWithHoles", "arr", "Array", "isArray"], "sources": ["../../src/helpers/arrayWithHoles.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _arrayWithHoles<T>(arr: Array<T>) {\n  if (Array.isArray(arr)) return arr;\n}\n"], "mappings": ";;;;;;AAEe,SAASA,eAAeA,CAAIC,GAAa,EAAE;EACxD,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE,OAAOA,GAAG;AACpC", "ignoreList": []}