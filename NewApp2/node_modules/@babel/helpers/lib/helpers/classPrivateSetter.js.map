{"version": 3, "names": ["_assert<PERSON>lassBrand", "require", "_classPrivateSetter", "privateMap", "setter", "receiver", "value", "assertClassBrand"], "sources": ["../../src/helpers/classPrivateSetter.ts"], "sourcesContent": ["/* @minVersion 7.24.0 */\n\nimport assertClassBrand from \"./assertClassBrand.ts\";\n\nexport default function _classPrivateSetter(\n  privateMap: WeakMap<any, any> | WeakSet<any>,\n  setter: Function,\n  receiver: any,\n  value: any,\n) {\n  setter(assertClassBrand(privateMap, receiver), value);\n  return value;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AAEe,SAASC,mBAAmBA,CACzCC,UAA4C,EAC5CC,MAAgB,EAChBC,QAAa,EACbC,KAAU,EACV;EACAF,MAAM,CAAC,IAAAG,yBAAgB,EAACJ,UAAU,EAAEE,QAAQ,CAAC,EAAEC,KAAK,CAAC;EACrD,OAAOA,KAAK;AACd", "ignoreList": []}