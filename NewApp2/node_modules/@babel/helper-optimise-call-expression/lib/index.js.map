{"version": 3, "names": ["_t", "require", "callExpression", "identifier", "isIdentifier", "isSpreadElement", "memberExpression", "optionalCallExpression", "optionalMemberExpression", "optimiseCallExpression", "callee", "thisNode", "args", "optional", "length", "argument", "name"], "sources": ["../src/index.ts"], "sourcesContent": ["import {\n  callExpression,\n  identifier,\n  isIdentifier,\n  isSpreadElement,\n  memberExpression,\n  optionalCallExpression,\n  optionalMemberExpression,\n} from \"@babel/types\";\nimport type {\n  CallExpression,\n  Expression,\n  OptionalCallExpression,\n} from \"@babel/types\";\n\n/**\n * A helper function that generates a new call expression with given thisNode.\n It will also optimize `(...arguments)` to `.apply(arguments)`\n *\n * @export\n * @param {Expression} callee The callee of call expression\n * @param {Expression} thisNode The desired this of call expression\n * @param {Readonly<CallExpression[\"arguments\"]>} args The arguments of call expression\n * @param {boolean} optional Whether the call expression is optional\n * @returns {CallExpression | OptionalCallExpression} The generated new call expression\n */\nexport default function optimiseCallExpression(\n  callee: Expression,\n  thisNode: Expression,\n  args: Readonly<CallExpression[\"arguments\"]>,\n  optional: boolean,\n): CallExpression | OptionalCallExpression {\n  if (\n    args.length === 1 &&\n    isSpreadElement(args[0]) &&\n    isIdentifier(args[0].argument, { name: \"arguments\" })\n  ) {\n    // a.b?.(...arguments);\n    if (optional) {\n      return optionalCallExpression(\n        optionalMemberExpression(callee, identifier(\"apply\"), false, true),\n        [thisNode, args[0].argument],\n        false,\n      );\n    }\n    // a.b(...arguments);\n    return callExpression(memberExpression(callee, identifier(\"apply\")), [\n      thisNode,\n      args[0].argument,\n    ]);\n  } else {\n    // a.b?.(arg1, arg2)\n    if (optional) {\n      return optionalCallExpression(\n        optionalMemberExpression(callee, identifier(\"call\"), false, true),\n        [thisNode, ...args],\n        false,\n      );\n    }\n    // a.b(arg1, arg2)\n    return callExpression(memberExpression(callee, identifier(\"call\")), [\n      thisNode,\n      ...args,\n    ]);\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAQsB;EAPpBC,cAAc;EACdC,UAAU;EACVC,YAAY;EACZC,eAAe;EACfC,gBAAgB;EAChBC,sBAAsB;EACtBC;AAAwB,IAAAR,EAAA;AAmBX,SAASS,sBAAsBA,CAC5CC,MAAkB,EAClBC,QAAoB,EACpBC,IAA2C,EAC3CC,QAAiB,EACwB;EACzC,IACED,IAAI,CAACE,MAAM,KAAK,CAAC,IACjBT,eAAe,CAACO,IAAI,CAAC,CAAC,CAAC,CAAC,IACxBR,YAAY,CAACQ,IAAI,CAAC,CAAC,CAAC,CAACG,QAAQ,EAAE;IAAEC,IAAI,EAAE;EAAY,CAAC,CAAC,EACrD;IAEA,IAAIH,QAAQ,EAAE;MACZ,OAAON,sBAAsB,CAC3BC,wBAAwB,CAACE,MAAM,EAAEP,UAAU,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,EAClE,CAACQ,QAAQ,EAAEC,IAAI,CAAC,CAAC,CAAC,CAACG,QAAQ,CAAC,EAC5B,KACF,CAAC;IACH;IAEA,OAAOb,cAAc,CAACI,gBAAgB,CAACI,MAAM,EAAEP,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,CACnEQ,QAAQ,EACRC,IAAI,CAAC,CAAC,CAAC,CAACG,QAAQ,CACjB,CAAC;EACJ,CAAC,MAAM;IAEL,IAAIF,QAAQ,EAAE;MACZ,OAAON,sBAAsB,CAC3BC,wBAAwB,CAACE,MAAM,EAAEP,UAAU,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,EACjE,CAACQ,QAAQ,EAAE,GAAGC,IAAI,CAAC,EACnB,KACF,CAAC;IACH;IAEA,OAAOV,cAAc,CAACI,gBAAgB,CAACI,MAAM,EAAEP,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAClEQ,QAAQ,EACR,GAAGC,IAAI,CACR,CAAC;EACJ;AACF", "ignoreList": []}