{"version": 3, "names": ["_t", "require", "isParenthesizedExpression", "isTSAsExpression", "isTSNonNullExpression", "isTSSatisfiesExpression", "isTSTypeAssertion", "isTypeCastExpression", "isTransparentExprWrapper", "node", "skipTransparentExprWrappers", "path", "get", "skipTransparentExprWrapperNodes", "expression"], "sources": ["../src/index.ts"], "sourcesContent": ["import {\n  isParenthesizedExpression,\n  isTSAsExpression,\n  isTSNonNullExpression,\n  isTSSatisfiesExpression,\n  isTSTypeAssertion,\n  isTypeCastExpression,\n} from \"@babel/types\";\n\nimport type * as t from \"@babel/types\";\nimport type { NodePath } from \"@babel/traverse\";\n\nexport type TransparentExprWrapper =\n  | t.TSAsExpression\n  | t.TSSatisfiesExpression\n  | t.TSTypeAssertion\n  | t.TSNonNullExpression\n  | t.TypeCastExpression\n  | t.ParenthesizedExpression;\n\n// A transparent expression wrapper is an AST node that most plugins will wish\n// to skip, as its presence does not affect the behaviour of the code. This\n// includes expressions used for types, and extra parenthesis. For example, in\n// (a as any)(), this helper can be used to skip the TSAsExpression when\n// determining the callee.\nexport function isTransparentExprWrapper(\n  node: t.Node,\n): node is TransparentExprWrapper {\n  return (\n    isTSAsExpression(node) ||\n    isTSSatisfiesExpression(node) ||\n    isTSTypeAssertion(node) ||\n    isTSNonNullExpression(node) ||\n    isTypeCastExpression(node) ||\n    isParenthesizedExpression(node)\n  );\n}\n\nexport function skipTransparentExprWrappers(\n  path: NodePath<t.Expression>,\n): NodePath<t.Expression> {\n  while (isTransparentExprWrapper(path.node)) {\n    path = path.get(\"expression\");\n  }\n  return path;\n}\n\nexport function skipTransparentExprWrapperNodes(\n  node: t.Expression | t.Super,\n): t.Expression | t.Super {\n  while (isTransparentExprWrapper(node)) {\n    node = node.expression;\n  }\n  return node;\n}\n"], "mappings": ";;;;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAOsB;EANpBC,yBAAyB;EACzBC,gBAAgB;EAChBC,qBAAqB;EACrBC,uBAAuB;EACvBC,iBAAiB;EACjBC;AAAoB,IAAAP,EAAA;AAmBf,SAASQ,wBAAwBA,CACtCC,IAAY,EACoB;EAChC,OACEN,gBAAgB,CAACM,IAAI,CAAC,IACtBJ,uBAAuB,CAACI,IAAI,CAAC,IAC7BH,iBAAiB,CAACG,IAAI,CAAC,IACvBL,qBAAqB,CAACK,IAAI,CAAC,IAC3BF,oBAAoB,CAACE,IAAI,CAAC,IAC1BP,yBAAyB,CAACO,IAAI,CAAC;AAEnC;AAEO,SAASC,2BAA2BA,CACzCC,IAA4B,EACJ;EACxB,OAAOH,wBAAwB,CAACG,IAAI,CAACF,IAAI,CAAC,EAAE;IAC1CE,IAAI,GAAGA,IAAI,CAACC,GAAG,CAAC,YAAY,CAAC;EAC/B;EACA,OAAOD,IAAI;AACb;AAEO,SAASE,+BAA+BA,CAC7CJ,IAA4B,EACJ;EACxB,OAAOD,wBAAwB,CAACC,IAAI,CAAC,EAAE;IACrCA,IAAI,GAAGA,IAAI,CAACK,UAAU;EACxB;EACA,OAAOL,IAAI;AACb", "ignoreList": []}